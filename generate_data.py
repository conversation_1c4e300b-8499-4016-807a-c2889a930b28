#!/usr/bin/env python
"""
Script để tạo dữ liệu phong phú cho hệ thống quản lý sinh viên
- 500+ sinh viên
- Nhiều môn học đại học từ các ngành khác nhau
- Dữ liệu đăng ký môn học với điểm số
"""
import os
import sys
import django
import random
from datetime import datetime, timedelta
from faker import Faker

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from courses.models import Course, Enrollment
from django.db import transaction

# Khởi tạo Faker với locale tiếng Việt
fake = Faker('vi_VN')
Faker.seed(42)  # <PERSON><PERSON> có kết quả nhất quán

# <PERSON><PERSON> sách các ngành học và môn học tương ứng
MAJORS_AND_COURSES = {
    "Công nghệ thông tin": [
        ("CS101", "Nhập môn lập trình", 3),
        ("<PERSON>102", "<PERSON><PERSON>u trúc dữ liệu và giải thuật", 4),
        ("<PERSON>103", "Lập tr<PERSON>nh hướng đối t<PERSON><PERSON>ng", 3),
        ("CS104", "Cơ sở d<PERSON> li<PERSON>u", 3),
        ("<PERSON>105", "M<PERSON>ng m<PERSON>y tính", 3),
        ("CS106", "H<PERSON> điều hành", 3),
        ("CS107", "Công nghệ phần mềm", 4),
        ("CS108", "Trí tuệ nhân tạo", 3),
        ("CS109", "Học máy", 3),
        ("CS110", "Bảo mật thông tin", 3),
        ("CS111", "Phát triển ứng dụng web", 3),
        ("CS112", "Phát triển ứng dụng di động", 3),
        ("CS113", "Kiến trúc máy tính", 3),
        ("CS114", "Đồ họa máy tính", 3),
        ("CS115", "Xử lý ảnh số", 3),
    ],
    "Kinh tế": [
        ("EC101", "Kinh tế học đại cương", 3),
        ("EC102", "Kinh tế vi mô", 3),
        ("EC103", "Kinh tế vĩ mô", 3),
        ("EC104", "Thống kê kinh tế", 3),
        ("EC105", "Tài chính doanh nghiệp", 3),
        ("EC106", "Marketing căn bản", 3),
        ("EC107", "Quản trị nhân lực", 3),
        ("EC108", "Kế toán tài chính", 4),
        ("EC109", "Phân tích đầu tư", 3),
        ("EC110", "Thương mại quốc tế", 3),
        ("EC111", "Ngân hàng và tín dụng", 3),
        ("EC112", "Bảo hiểm", 2),
        ("EC113", "Kinh tế lượng", 3),
        ("EC114", "Tài chính công", 3),
        ("EC115", "Kinh tế phát triển", 3),
    ],
    "Kỹ thuật": [
        ("EN101", "Vật lý đại cương", 4),
        ("EN102", "Toán cao cấp", 4),
        ("EN103", "Hóa học đại cương", 3),
        ("EN104", "Cơ học kỹ thuật", 3),
        ("EN105", "Điện tử cơ bản", 3),
        ("EN106", "Kỹ thuật điện", 3),
        ("EN107", "Cơ khí chính xác", 3),
        ("EN108", "Vật liệu kỹ thuật", 3),
        ("EN109", "Tự động hóa", 3),
        ("EN110", "Đo lường và điều khiển", 3),
        ("EN111", "Thiết kế kỹ thuật", 3),
        ("EN112", "An toàn lao động", 2),
        ("EN113", "Quản lý dự án", 3),
        ("EN114", "Công nghệ sản xuất", 3),
        ("EN115", "Bảo trì thiết bị", 2),
    ],
    "Y học": [
        ("MD101", "Giải phẫu học", 4),
        ("MD102", "Sinh lý học", 4),
        ("MD103", "Sinh hóa y học", 3),
        ("MD104", "Vi sinh y học", 3),
        ("MD105", "Dược lý học", 4),
        ("MD106", "Bệnh lý học", 4),
        ("MD107", "Nội khoa", 5),
        ("MD108", "Ngoại khoa", 5),
        ("MD109", "Sản phụ khoa", 4),
        ("MD110", "Nhi khoa", 4),
        ("MD111", "Tâm thần học", 3),
        ("MD112", "Da liễu", 3),
        ("MD113", "Mắt", 2),
        ("MD114", "Tai mũi họng", 3),
        ("MD115", "Y học cộng đồng", 3),
    ],
    "Luật": [
        ("LW101", "Lý luận nhà nước và pháp luật", 3),
        ("LW102", "Luật hiến pháp", 3),
        ("LW103", "Luật hành chính", 3),
        ("LW104", "Luật dân sự", 4),
        ("LW105", "Luật hình sự", 4),
        ("LW106", "Luật lao động", 3),
        ("LW107", "Luật thương mại", 3),
        ("LW108", "Luật tài chính", 3),
        ("LW109", "Luật quốc tế", 3),
        ("LW110", "Tố tụng dân sự", 3),
        ("LW111", "Tố tụng hình sự", 3),
        ("LW112", "Luật kinh tế", 3),
        ("LW113", "Luật môi trường", 2),
        ("LW114", "Luật gia đình", 2),
        ("LW115", "Luật sở hữu trí tuệ", 3),
    ],
    "Ngôn ngữ": [
        ("LG101", "Ngữ pháp tiếng Anh", 3),
        ("LG102", "Nghe nói tiếng Anh", 3),
        ("LG103", "Đọc hiểu tiếng Anh", 3),
        ("LG104", "Viết tiếng Anh", 3),
        ("LG105", "Văn học Anh - Mỹ", 3),
        ("LG106", "Dịch thuật", 3),
        ("LG107", "Ngôn ngữ học đại cương", 3),
        ("LG108", "Tiếng Anh thương mại", 3),
        ("LG109", "Tiếng Anh học thuật", 3),
        ("LG110", "Văn hóa các nước nói tiếng Anh", 2),
        ("LG111", "Phương pháp giảng dạy", 3),
        ("LG112", "Tiếng Trung cơ bản", 3),
        ("LG113", "Tiếng Nhật cơ bản", 3),
        ("LG114", "Tiếng Hàn cơ bản", 3),
        ("LG115", "Tiếng Pháp cơ bản", 3),
    ]
}

# Môn học chung cho tất cả ngành
COMMON_COURSES = [
    ("GE101", "Triết học Mác - Lênin", 3),
    ("GE102", "Kinh tế chính trị Mác - Lênin", 2),
    ("GE103", "Chủ nghĩa xã hội khoa học", 2),
    ("GE104", "Lịch sử Đảng Cộng sản Việt Nam", 2),
    ("GE105", "Tư tưởng Hồ Chí Minh", 2),
    ("GE106", "Pháp luật đại cương", 2),
    ("GE107", "Tiếng Anh 1", 3),
    ("GE108", "Tiếng Anh 2", 3),
    ("GE109", "Tiếng Anh 3", 3),
    ("GE110", "Thể dục 1", 1),
    ("GE111", "Thể dục 2", 1),
    ("GE112", "Giáo dục quốc phòng", 4),
    ("GE113", "Tin học đại cương", 3),
    ("GE114", "Toán cao cấp A1", 3),
    ("GE115", "Toán cao cấp A2", 3),
]

# Danh sách tỉnh thành Việt Nam
PROVINCES = [
    "Hà Nội", "TP. Hồ Chí Minh", "Đà Nẵng", "Hải Phòng", "Cần Thơ",
    "An Giang", "Bà Rịa - Vũng Tàu", "Bắc Giang", "Bắc Kạn", "Bạc Liêu",
    "Bắc Ninh", "Bến Tre", "Bình Định", "Bình Dương", "Bình Phước",
    "Bình Thuận", "Cà Mau", "Cao Bằng", "Đắk Lắk", "Đắk Nông",
    "Điện Biên", "Đồng Nai", "Đồng Tháp", "Gia Lai", "Hà Giang",
    "Hà Nam", "Hà Tĩnh", "Hải Dương", "Hậu Giang", "Hòa Bình",
    "Hưng Yên", "Khánh Hòa", "Kiên Giang", "Kon Tum", "Lai Châu",
    "Lâm Đồng", "Lạng Sơn", "Lào Cai", "Long An", "Nam Định",
    "Nghệ An", "Ninh Bình", "Ninh Thuận", "Phú Thọ", "Phú Yên",
    "Quảng Bình", "Quảng Nam", "Quảng Ngãi", "Quảng Ninh", "Quảng Trị",
    "Sóc Trăng", "Sơn La", "Tây Ninh", "Thái Bình", "Thái Nguyên",
    "Thanh Hóa", "Thừa Thiên Huế", "Tiền Giang", "Trà Vinh", "Tuyên Quang",
    "Vĩnh Long", "Vĩnh Phúc", "Yên Bái"
]

def get_random_grade():
    """Tạo điểm ngẫu nhiên theo phân phối thực tế"""
    rand = random.random()
    if rand < 0.05:  # 5% điểm F
        return random.uniform(0, 3.9), 'F'
    elif rand < 0.15:  # 10% điểm D
        return random.uniform(4.0, 5.4), 'D'
    elif rand < 0.35:  # 20% điểm C
        return random.uniform(5.5, 6.9), 'C'
    elif rand < 0.70:  # 35% điểm B
        return random.uniform(7.0, 8.4), 'B'
    else:  # 30% điểm A
        return random.uniform(8.5, 10.0), 'A'

def generate_student_id(index):
    """Tạo mã sinh viên theo format năm + số thứ tự"""
    year = random.choice([2020, 2021, 2022, 2023, 2024])
    return f"{year}{index:04d}"

def generate_phone():
    """Tạo số điện thoại Việt Nam"""
    prefixes = ['090', '091', '094', '083', '084', '085', '081', '082', '032', '033', '034', '035', '036', '037', '038', '039']
    return f"{random.choice(prefixes)}{random.randint(1000000, 9999999)}"

def create_courses():
    """Tạo tất cả môn học"""
    print("📚 Tạo môn học...")

    all_courses = []

    # Thêm môn học chung
    for course_code, name, credits in COMMON_COURSES:
        all_courses.append((course_code, name, credits, "Chung"))

    # Thêm môn học theo ngành
    for major, courses in MAJORS_AND_COURSES.items():
        for course_code, name, credits in courses:
            all_courses.append((course_code, name, credits, major))

    created_count = 0
    for course_code, name, credits, major in all_courses:
        course, created = Course.objects.get_or_create(
            course_code=course_code,
            defaults={
                'name': name,
                'description': f"Môn học {name} thuộc ngành {major}",
                'credits': credits,
                'semester': random.choice([1, 2]),
                'year': random.choice([2023, 2024]),
                'start_date': fake.date_between(start_date='-1y', end_date='today'),
                'end_date': fake.date_between(start_date='today', end_date='+6m'),
                'is_active': random.choice([True, True, True, False])  # 75% active
            }
        )
        if created:
            created_count += 1

    print(f"✓ Đã tạo {created_count} môn học mới")
    return Course.objects.all()

def create_students(count=500):
    """Tạo sinh viên với thông tin đa dạng"""
    print(f"👥 Tạo {count} sinh viên...")

    majors = list(MAJORS_AND_COURSES.keys())
    created_count = 0

    for i in range(1, count + 1):
        student_id = generate_student_id(i)

        # Kiểm tra xem sinh viên đã tồn tại chưa
        if Student.objects.filter(student_id=student_id).exists():
            continue

        # Tạo thông tin sinh viên
        first_name = fake.first_name()
        last_name = fake.last_name()
        email = f"{student_id}@student.edu.vn"

        student = Student.objects.create(
            student_id=student_id,
            first_name=first_name,
            last_name=last_name,
            date_of_birth=fake.date_between(start_date='-25y', end_date='-18y'),
            gender=random.choice(['M', 'F']),
            email=email,
            phone_number=generate_phone(),
            address=f"{fake.street_address()}, {random.choice(PROVINCES)}",
            enrollment_date=fake.date_between(start_date='-4y', end_date='today'),
            is_active=random.choice([True, True, True, True, False])  # 80% active
        )
        created_count += 1

        if created_count % 50 == 0:
            print(f"  ✓ Đã tạo {created_count} sinh viên...")

    print(f"✓ Đã tạo {created_count} sinh viên mới")
    return Student.objects.all()

def create_enrollments(students, courses):
    """Tạo dữ liệu đăng ký môn học"""
    print("📝 Tạo dữ liệu đăng ký môn học...")

    created_count = 0
    total_students = len(students)

    for i, student in enumerate(students):
        # Mỗi sinh viên đăng ký 8-15 môn học ngẫu nhiên
        num_courses = random.randint(8, 15)
        selected_courses = random.sample(list(courses), min(num_courses, len(courses)))

        for course in selected_courses:
            # Kiểm tra xem đã đăng ký chưa
            if Enrollment.objects.filter(student=student, course=course).exists():
                continue

            # Tạo enrollment
            enrollment_date = fake.date_between(
                start_date=max(student.enrollment_date, course.start_date),
                end_date='today'
            )

            # 80% sinh viên có điểm, 20% chưa có điểm
            has_grade = random.random() < 0.8

            if has_grade:
                numeric_grade, letter_grade = get_random_grade()
            else:
                numeric_grade, letter_grade = None, None

            Enrollment.objects.create(
                student=student,
                course=course,
                enrollment_date=enrollment_date,
                grade=letter_grade,
                numeric_grade=round(numeric_grade, 1) if numeric_grade else None
            )
            created_count += 1

        if (i + 1) % 50 == 0:
            print(f"  ✓ Đã xử lý {i + 1}/{total_students} sinh viên...")

    print(f"✓ Đã tạo {created_count} bản ghi đăng ký môn học")

def print_statistics():
    """In thống kê dữ liệu"""
    print("\n📊 THỐNG KÊ DỮ LIỆU")
    print("=" * 40)

    total_students = Student.objects.count()
    active_students = Student.objects.filter(is_active=True).count()
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    total_enrollments = Enrollment.objects.count()
    graded_enrollments = Enrollment.objects.filter(grade__isnull=False).count()

    print(f"👥 Tổng số sinh viên: {total_students}")
    print(f"   - Đang học: {active_students}")
    print(f"   - Tạm nghỉ: {total_students - active_students}")

    print(f"📚 Tổng số môn học: {total_courses}")
    print(f"   - Đang mở: {active_courses}")
    print(f"   - Đã đóng: {total_courses - active_courses}")

    print(f"📝 Tổng số đăng ký: {total_enrollments}")
    print(f"   - Đã có điểm: {graded_enrollments}")
    print(f"   - Chưa có điểm: {total_enrollments - graded_enrollments}")

    # Thống kê điểm
    if graded_enrollments > 0:
        from django.db.models import Avg, Count
        avg_grade = Enrollment.objects.filter(numeric_grade__isnull=False).aggregate(
            avg=Avg('numeric_grade')
        )['avg']

        grade_distribution = Enrollment.objects.filter(grade__isnull=False).values('grade').annotate(
            count=Count('grade')
        ).order_by('grade')

        print(f"📈 Điểm trung bình: {avg_grade:.2f}")
        print("📊 Phân phối điểm:")
        for item in grade_distribution:
            percentage = (item['count'] / graded_enrollments) * 100
            print(f"   - {item['grade']}: {item['count']} ({percentage:.1f}%)")

def main():
    """Hàm chính để tạo tất cả dữ liệu"""
    print("🎯 BẮT ĐẦU TẠO DỮ LIỆU PHONG PHÚ CHO DATABASE")
    print("=" * 60)

    try:
        with transaction.atomic():
            # Bước 1: Tạo môn học
            courses = create_courses()

            # Bước 2: Tạo sinh viên
            students = create_students(500)

            # Bước 3: Tạo dữ liệu đăng ký
            create_enrollments(students, courses)

            # Bước 4: In thống kê
            print_statistics()

            print("\n🎉 HOÀN THÀNH TẠO DỮ LIỆU!")
            print("=" * 60)
            print("✅ Dữ liệu đã được tạo thành công!")
            print("🌐 Bạn có thể truy cập http://127.0.0.1:8000 để xem kết quả")

    except Exception as e:
        print(f"❌ Lỗi khi tạo dữ liệu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
