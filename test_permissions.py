#!/usr/bin/env python
"""
Script để test hệ thống phân quyền
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from courses.models import Course, Enrollment
from django.test import Client
from django.urls import reverse

def create_test_users():
    """Tạo user test"""
    print("=== Tạo user test ===")
    
    # Tạo admin user
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'Test',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✓ Tạo admin user: {admin_user.username}")
    else:
        print(f"✓ Admin user đã tồn tại: {admin_user.username}")
    
    # Tạo student user
    student_user, created = User.objects.get_or_create(
        username='student_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Student',
            'last_name': 'Test',
            'is_staff': False,
            'is_superuser': False
        }
    )
    if created:
        student_user.set_password('student123')
        student_user.save()
        print(f"✓ Tạo student user: {student_user.username}")
    else:
        print(f"✓ Student user đã tồn tại: {student_user.username}")
    
    # Tạo Student record cho student_user
    student_record, created = Student.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'student_id': 'SV001',
            'first_name': 'Student',
            'last_name': 'Test',
            'date_of_birth': '2000-01-01',
            'gender': 'M',
            'phone_number': '0123456789',
            'address': 'Test Address',
            'is_active': True
        }
    )
    if created:
        print(f"✓ Tạo Student record: {student_record.student_id}")
    else:
        print(f"✓ Student record đã tồn tại: {student_record.student_id}")
    
    return admin_user, student_user, student_record

def test_admin_access():
    """Test quyền truy cập admin"""
    print("\n=== Test quyền truy cập admin ===")
    
    client = Client()
    
    # Test với admin user
    client.login(username='admin_test', password='admin123')
    
    admin_urls = [
        '/dashboard/',
        '/reports/',
        '/students/',
        '/students/create/',
        '/courses/',
        '/courses/create/',
        '/courses/enrollments/',
        '/courses/enrollments/create/',
    ]
    
    for url in admin_urls:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✓ Admin có thể truy cập {url}")
            elif response.status_code == 302:
                print(f"⚠ Admin bị redirect từ {url} -> {response.url}")
            else:
                print(f"✗ Admin không thể truy cập {url} (status: {response.status_code})")
        except Exception as e:
            print(f"✗ Lỗi khi truy cập {url}: {e}")

def test_student_access():
    """Test quyền truy cập sinh viên"""
    print("\n=== Test quyền truy cập sinh viên ===")
    
    client = Client()
    
    # Test với student user
    client.login(username='student_test', password='student123')
    
    # URLs mà sinh viên KHÔNG được truy cập
    forbidden_urls = [
        '/dashboard/',
        '/reports/',
        '/students/create/',
        '/courses/create/',
        '/courses/enrollments/create/',
    ]
    
    for url in forbidden_urls:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✗ LỖII: Sinh viên có thể truy cập {url} (không được phép!)")
            elif response.status_code == 302:
                print(f"✓ Sinh viên bị redirect từ {url} (đúng)")
            elif response.status_code == 403:
                print(f"✓ Sinh viên bị từ chối truy cập {url} (đúng)")
            else:
                print(f"? Sinh viên truy cập {url} -> status: {response.status_code}")
        except Exception as e:
            print(f"✗ Lỗi khi truy cập {url}: {e}")
    
    # URLs mà sinh viên ĐƯỢC truy cập
    allowed_urls = [
        '/',
        '/student-profile/',
        '/my-courses/',
    ]
    
    for url in allowed_urls:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✓ Sinh viên có thể truy cập {url}")
            elif response.status_code == 302:
                print(f"⚠ Sinh viên bị redirect từ {url} -> {response.url}")
            else:
                print(f"✗ Sinh viên không thể truy cập {url} (status: {response.status_code})")
        except Exception as e:
            print(f"✗ Lỗi khi truy cập {url}: {e}")

def main():
    print("🔒 KIỂM TRA HỆ THỐNG PHÂN QUYỀN")
    print("=" * 50)
    
    # Tạo test users
    admin_user, student_user, student_record = create_test_users()
    
    # Test admin access
    test_admin_access()
    
    # Test student access
    test_student_access()
    
    print("\n" + "=" * 50)
    print("✅ HOÀN THÀNH KIỂM TRA")
    print("\nThông tin đăng nhập test:")
    print("Admin: admin_test / admin123")
    print("Student: student_test / student123")

if __name__ == '__main__':
    main()
