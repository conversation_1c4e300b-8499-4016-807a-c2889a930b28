#!/usr/bin/env python
"""
Test script để kiểm tra views cho sinh viên
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from students.models import Student

def test_student_views():
    """Test các view cho sinh viên"""
    print("🧪 KIỂM TRA VIEWS CHO SINH VIÊN")
    print("=" * 50)
    
    # Tạo client
    client = Client()
    
    # Đăng nhập với tài khoản sinh viên
    login_success = client.login(username='student_test', password='student123')
    print(f"✓ Đăng nhập sinh viên: {'Thành công' if login_success else 'Thất bại'}")
    
    if not login_success:
        print("❌ Không thể đăng nhập. <PERSON><PERSON><PERSON> tra tài khoản.")
        return
    
    # Test student-profile
    print("\n📋 Test /student-profile/")
    try:
        response = client.get('/student-profile/')
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            print("✅ student-profile hoạt động!")
        elif response.status_code == 302:
            print(f"⚠️ Bị redirect đến: {response.url}")
        else:
            print(f"❌ Lỗi: {response.status_code}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test my-courses
    print("\n📚 Test /my-courses/")
    try:
        response = client.get('/my-courses/')
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            print("✅ my-courses hoạt động!")
        elif response.status_code == 302:
            print(f"⚠️ Bị redirect đến: {response.url}")
        else:
            print(f"❌ Lỗi: {response.status_code}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Kiểm tra dữ liệu
    print("\n📊 Kiểm tra dữ liệu:")
    user = User.objects.get(username='student_test')
    print(f"User email: {user.email}")
    print(f"User is_staff: {user.is_staff}")
    
    students_with_email = Student.objects.filter(email=user.email)
    print(f"Students với email {user.email}: {students_with_email.count()}")
    
    if students_with_email.exists():
        student = students_with_email.first()
        print(f"Student tìm thấy: {student.student_id} - {student.first_name} {student.last_name}")
    else:
        print("❌ Không tìm thấy student với email này")
        print("Thử tìm student đầu tiên:")
        first_student = Student.objects.first()
        if first_student:
            print(f"Student đầu tiên: {first_student.student_id} - {first_student.first_name} {first_student.last_name}")

if __name__ == '__main__':
    test_student_views()
