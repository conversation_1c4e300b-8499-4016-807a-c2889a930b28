{% extends 'base.html' %}

{% block title %}Trang chủ - <PERSON><PERSON> thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-md-12 text-center">
            <h1 class="display-4">H<PERSON> thống Quản lý Sinh viên</h1>
            <p class="lead">Quản lý thông tin sinh viên, môn học và đăng ký học một cách hiệu quả</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-5">
        <div class="col-md-3 mb-4">
            <div class="card text-center h-100 border-primary">
                <div class="card-body">
                    <i class="fas fa-user-graduate fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Tổng số sinh viên</h5>
                    <p class="card-text display-6">{{ total_students }}</p>
                    <p class="card-text text-muted">{{ active_students }} đang học</p>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{% url 'student-list' %}" class="btn btn-outline-primary">Xem danh sách</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-center h-100 border-success">
                <div class="card-body">
                    <i class="fas fa-book fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Tổng số môn học</h5>
                    <p class="card-text display-6">{{ total_courses }}</p>
                    <p class="card-text text-muted">{{ active_courses }} đang mở</p>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{% url 'course-list' %}" class="btn btn-outline-success">Xem danh sách</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-center h-100 border-info">
                <div class="card-body">
                    <i class="fas fa-clipboard-list fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Đăng ký học</h5>
                    <p class="card-text display-6">{{ total_enrollments }}</p>
                    <p class="card-text text-muted">Tổng số đăng ký</p>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{% url 'enrollment-list' %}" class="btn btn-outline-info">Xem đăng ký</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-center h-100 border-warning">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Thống kê</h5>
                    <p class="card-text">Xem thống kê chi tiết về sinh viên và môn học</p>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{% url 'dashboard' %}" class="btn btn-outline-warning">Xem thống kê</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Enrollments -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Đăng ký gần đây</h5>
                </div>
                <div class="card-body">
                    {% if recent_enrollments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Sinh viên</th>
                                    <th>Môn học</th>
                                    <th>Ngày đăng ký</th>
                                    <th>Điểm</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in recent_enrollments %}
                                <tr>
                                    <td>
                                        <a href="{% url 'student-detail' enrollment.student.id %}">
                                            {{ enrollment.student }}
                                        </a>
                                    </td>
                                    <td>
                                        <a href="{% url 'course-detail' enrollment.course.id %}">
                                            {{ enrollment.course }}
                                        </a>
                                    </td>
                                    <td>{{ enrollment.enrollment_date }}</td>
                                    <td>
                                        {% if enrollment.grade %}
                                            <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Chưa có điểm</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-center">Chưa có đăng ký nào.</p>
                    {% endif %}
                </div>
                <div class="card-footer text-end">
                    <a href="{% url 'enrollment-list' %}" class="btn btn-primary">Xem tất cả</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
