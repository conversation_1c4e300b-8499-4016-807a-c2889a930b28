/* Custom styles for Student Management System */

/* General Styles */
body {
    font-family: 'Roboto', sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

.content {
    min-height: calc(100vh - 160px);
}

/* Navbar Customization */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
}

.nav-link.active {
    font-weight: 700;
}

/* Card Customization */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 0.25rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 0.25rem;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Date Input Styling */
input[type="date"] {
    position: relative;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23666" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px;
    cursor: pointer;
    opacity: 0.7;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* Date input wrapper */
.date-input-wrapper {
    position: relative;
}

.date-input-wrapper::before {
    content: '\f073';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    z-index: 1;
}

/* Student ID input styling */
input[name="student_id"] {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

input[name="student_id"]:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

input[name="student_id"].is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* Character counter for student ID */
.student-id-counter {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: right;
    margin-top: 0.25rem;
}

.student-id-counter.warning {
    color: #fd7e14;
}

.student-id-counter.danger {
    color: #dc3545;
}

/* Logout button styling */
.dropdown-item button.dropdown-item {
    border: none !important;
    background: none !important;
    width: 100% !important;
    text-align: left !important;
    padding: 0.25rem 1rem !important;
    color: #212529 !important;
    text-decoration: none !important;
    display: block !important;
    clear: both !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    white-space: nowrap !important;
}

.dropdown-item button.dropdown-item:hover {
    background-color: #e9ecef !important;
    color: #1e2125 !important;
}

/* Registration form styling */
.register-form .form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.register-form .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.register-form .input-group-text {
    border-radius: 0.5rem 0 0 0.5rem;
    border: 2px solid #e9ecef;
    border-right: none;
    background-color: #f8f9fa;
}

.register-form .btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.register-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Role selection styling */
.role-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.role-card:hover {
    border-color: #0d6efd;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.role-card.selected {
    border-color: #0d6efd;
    background: #f8f9ff;
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

.role-card .role-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.role-card h5 {
    color: #333;
    margin-bottom: 10px;
}

.role-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Tables */
.table {
    vertical-align: middle;
}

.table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Pagination */
.pagination .page-link {
    color: #0d6efd;
    border-radius: 0.25rem;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Footer */
footer {
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

/* Dashboard Stats */
.stat-card {
    border-left: 4px solid;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.border-primary {
    border-left-color: #0d6efd;
}

.stat-card.border-success {
    border-left-color: #198754;
}

.stat-card.border-info {
    border-left-color: #0dcaf0;
}

.stat-card.border-warning {
    border-left-color: #ffc107;
}

/* Profile Image */
.profile-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.25rem;
    }

    .profile-img {
        width: 100px;
        height: 100px;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        font-size: 12pt;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }
}

/* Custom animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
