#!/usr/bin/env python
"""
Script để tạo User accounts cho tất cả Students
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from django.db import transaction

def create_student_users():
    """Tạo User accounts cho tất cả Students"""
    print("👥 TẠO USER ACCOUNTS CHO TẤT CẢ STUDENTS")
    print("=" * 50)
    
    students = Student.objects.all()
    created_count = 0
    updated_count = 0
    
    with transaction.atomic():
        for student in students:
            # Tạo username từ student_id
            username = student.student_id.lower()
            
            # Kiểm tra xem user đã tồn tại chưa
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': student.email,
                    'first_name': student.first_name,
                    'last_name': student.last_name,
                    'is_staff': False,
                    'is_superuser': False,
                    'is_active': student.is_active
                }
            )
            
            if created:
                # Đặt password mặc định là student_id
                user.set_password(student.student_id)
                user.save()
                created_count += 1
                
                if created_count % 50 == 0:
                    print(f"  ✓ Đã tạo {created_count} user accounts...")
            else:
                # Cập nhật thông tin nếu user đã tồn tại
                user.email = student.email
                user.first_name = student.first_name
                user.last_name = student.last_name
                user.is_active = student.is_active
                user.save()
                updated_count += 1
    
    print(f"✅ Hoàn thành!")
    print(f"   - Đã tạo mới: {created_count} user accounts")
    print(f"   - Đã cập nhật: {updated_count} user accounts")
    print(f"   - Tổng students: {students.count()}")
    
    print("\n📋 THÔNG TIN ĐĂNG NHẬP:")
    print("Username: [student_id] (ví dụ: ********)")
    print("Password: [student_id] (ví dụ: ********)")
    print("\nVí dụ:")
    sample_students = students[:5]
    for student in sample_students:
        print(f"  - Username: {student.student_id.lower()}")
        print(f"    Password: {student.student_id}")
        print(f"    Tên: {student.first_name} {student.last_name}")
        print()

if __name__ == '__main__':
    create_student_users()
