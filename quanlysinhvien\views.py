from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import logout, login, authenticate
from django.contrib import messages
from students.models import Student
from courses.models import Course, Enrollment
from django.db.models import Count, Avg, F, Q, Case, When, Value, IntegerField
from django.template.defaulttags import register
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_protect
from .forms import CustomUserCreationForm
from .decorators import admin_required
from .mixins import StudentOnlyMixin

def home(request):
    """Home page view."""
    total_students = Student.objects.count()
    active_students = Student.objects.filter(is_active=True).count()
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    total_enrollments = Enrollment.objects.count()

    # Get recent enrollments
    recent_enrollments = Enrollment.objects.order_by('-enrollment_date')[:5]

    context = {
        'total_students': total_students,
        'active_students': active_students,
        'total_courses': total_courses,
        'active_courses': active_courses,
        'total_enrollments': total_enrollments,
        'recent_enrollments': recent_enrollments,
    }

    return render(request, 'home.html', context)

@admin_required
def dashboard(request):
    """Dashboard view for admin users only."""
    # Student statistics
    total_students = Student.objects.count()
    active_students = Student.objects.filter(is_active=True).count()
    inactive_students = total_students - active_students

    # Course statistics
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    inactive_courses = total_courses - active_courses

    # Enrollment statistics
    total_enrollments = Enrollment.objects.count()
    courses_by_enrollment = Course.objects.annotate(
        enrollment_count=Count('enrollments')
    ).order_by('-enrollment_count')[:5]

    # Grade statistics
    avg_grade = Enrollment.objects.filter(
        numeric_grade__isnull=False
    ).aggregate(avg=Avg('numeric_grade'))

    context = {
        'total_students': total_students,
        'active_students': active_students,
        'inactive_students': inactive_students,
        'total_courses': total_courses,
        'active_courses': active_courses,
        'inactive_courses': inactive_courses,
        'total_enrollments': total_enrollments,
        'courses_by_enrollment': courses_by_enrollment,
        'avg_grade': avg_grade,
    }

    return render(request, 'dashboard.html', context)

@register.filter
def get_item(dictionary, key):
    """Template filter to get an item from a dictionary by key."""
    return dictionary.get(key)

@admin_required
def reports(request):
    """Reports view for admin users only."""
    # Student statistics
    total_students = Student.objects.count()
    active_students = Student.objects.filter(is_active=True).count()
    inactive_students = total_students - active_students
    active_student_ratio = (active_students / total_students * 100) if total_students > 0 else 0

    # Course statistics
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    inactive_courses = total_courses - active_courses
    active_course_ratio = (active_courses / total_courses * 100) if total_courses > 0 else 0

    # Enrollment statistics
    total_enrollments = Enrollment.objects.count()
    graded_enrollments = Enrollment.objects.filter(grade__isnull=False).count()

    # Grade statistics
    avg_grade = Enrollment.objects.filter(
        numeric_grade__isnull=False
    ).aggregate(avg=Avg('numeric_grade'))['avg'] or 0

    # Grade distribution
    grade_distribution = Enrollment.objects.filter(
        grade__isnull=False
    ).values('grade').annotate(count=Count('id')).order_by('grade')

    # Convert to dictionary for easier access in template
    grade_dist_dict = {item['grade']: item['count'] for item in grade_distribution}

    # Calculate percentages
    grade_percentages = {}
    for grade, count in grade_dist_dict.items():
        grade_percentages[grade] = (count / graded_enrollments * 100) if graded_enrollments > 0 else 0

    context = {
        'total_students': total_students,
        'active_students': active_students,
        'inactive_students': inactive_students,
        'active_student_ratio': active_student_ratio,
        'total_courses': total_courses,
        'active_courses': active_courses,
        'inactive_courses': inactive_courses,
        'active_course_ratio': active_course_ratio,
        'total_enrollments': total_enrollments,
        'graded_enrollments': graded_enrollments,
        'avg_grade': avg_grade,
        'grade_distribution': grade_dist_dict,
        'grade_percentages': grade_percentages,
    }

    return render(request, 'reports.html', context)

@require_POST
@csrf_protect
def custom_logout(request):
    """Custom logout view with proper handling."""
    if request.user.is_authenticated:
        username = request.user.username
        logout(request)
        messages.success(request, f'Bạn đã đăng xuất thành công. Hẹn gặp lại, {username}!')
    return render(request, 'auth/logout.html')

def register_view(request):
    """User registration view."""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')
            role = form.cleaned_data.get('role')
            role_display = 'Quản trị viên' if role == 'admin' else 'Sinh viên'

            messages.success(
                request,
                f'Tài khoản {username} đã được tạo thành công với vai trò {role_display}! Bạn có thể đăng nhập ngay bây giờ.'
            )

            # Auto login after registration
            authenticated_user = authenticate(
                username=form.cleaned_data['username'],
                password=form.cleaned_data['password1']
            )
            if authenticated_user:
                login(request, authenticated_user)
                messages.info(request, f'Chào mừng {user.get_full_name() or username} đến với hệ thống!')
                return redirect('home')
            else:
                return redirect('login')
    else:
        form = CustomUserCreationForm()

    return render(request, 'auth/register.html', {'form': form})


@login_required
def student_profile(request):
    """View for students to see their own profile information."""
    if request.user.is_staff:
        messages.info(request, 'Quản trị viên không có thông tin sinh viên.')
        return redirect('home')

    try:
        # Tìm thông tin sinh viên dựa trên username (student_id)
        student = Student.objects.filter(student_id__iexact=request.user.username).first()

        if not student:
            # Nếu không tìm thấy theo username, thử tìm theo email
            student = Student.objects.filter(email=request.user.email).first()

        if not student:
            # Nếu vẫn không tìm thấy, thử tìm student đầu tiên để demo
            student = Student.objects.first()
            if student:
                messages.info(request, f'Đang hiển thị thông tin demo cho sinh viên {student.student_id}. Vui lòng liên hệ quản trị viên để cập nhật thông tin chính xác.')
            else:
                messages.warning(request, 'Không tìm thấy thông tin sinh viên. Vui lòng liên hệ quản trị viên.')
                return redirect('home')

    except Exception as e:
        messages.error(request, f'Lỗi khi tìm thông tin sinh viên: {e}')
        return redirect('home')

    # Lấy danh sách môn học đã đăng ký
    enrollments = Enrollment.objects.filter(student=student).order_by('-enrollment_date')

    context = {
        'student': student,
        'enrollments': enrollments,
    }

    return render(request, 'students/student_profile.html', context)


@login_required
def my_courses(request):
    """View for students to see their enrolled courses."""
    if request.user.is_staff:
        messages.info(request, 'Trang này chỉ dành cho sinh viên.')
        return redirect('home')

    try:
        # Tìm thông tin sinh viên dựa trên username (student_id)
        student = Student.objects.filter(student_id__iexact=request.user.username).first()

        if not student:
            # Nếu không tìm thấy theo username, thử tìm theo email
            student = Student.objects.filter(email=request.user.email).first()

        if not student:
            # Nếu vẫn không tìm thấy, thử tìm student đầu tiên để demo
            student = Student.objects.first()
            if student:
                messages.info(request, f'Đang hiển thị thông tin demo cho sinh viên {student.student_id}. Vui lòng liên hệ quản trị viên để cập nhật thông tin chính xác.')
            else:
                messages.warning(request, 'Không tìm thấy thông tin sinh viên. Vui lòng liên hệ quản trị viên.')
                return redirect('home')

    except Exception as e:
        messages.error(request, f'Lỗi khi tìm thông tin sinh viên: {e}')
        return redirect('home')

    # Lấy danh sách môn học đã đăng ký
    enrollments = Enrollment.objects.filter(student=student).order_by('-enrollment_date')

    # Thống kê
    total_courses = enrollments.count()
    graded_courses = enrollments.filter(grade__isnull=False).count()
    avg_grade = enrollments.filter(numeric_grade__isnull=False).aggregate(avg=Avg('numeric_grade'))['avg']

    context = {
        'student': student,
        'enrollments': enrollments,
        'total_courses': total_courses,
        'graded_courses': graded_courses,
        'avg_grade': avg_grade,
    }

    return render(request, 'students/my_courses.html', context)
