{% extends 'base.html' %}

{% block title %}Báo cáo - <PERSON><PERSON> thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-file-alt me-2"></i>Báo cáo</h1>
        <div>
            <button class="btn btn-outline-primary btn-print">
                <i class="fas fa-print me-2"></i>In báo cáo
            </button>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Báo cáo sinh viên -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>B<PERSON>o cáo sinh viên</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Chỉ số</th>
                                    <th>Giá trị</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Tổng số sinh viên</td>
                                    <td>{{ total_students }}</td>
                                </tr>
                                <tr>
                                    <td>Sinh viên đang học</td>
                                    <td>{{ active_students }}</td>
                                </tr>
                                <tr>
                                    <td>Sinh viên đã nghỉ</td>
                                    <td>{{ inactive_students }}</td>
                                </tr>
                                <tr>
                                    <td>Tỷ lệ sinh viên đang học</td>
                                    <td>{{ active_student_ratio|floatformat:2 }}%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Báo cáo môn học -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-book me-2"></i>Báo cáo môn học</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Chỉ số</th>
                                    <th>Giá trị</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Tổng số môn học</td>
                                    <td>{{ total_courses }}</td>
                                </tr>
                                <tr>
                                    <td>Môn học đang mở</td>
                                    <td>{{ active_courses }}</td>
                                </tr>
                                <tr>
                                    <td>Môn học đã đóng</td>
                                    <td>{{ inactive_courses }}</td>
                                </tr>
                                <tr>
                                    <td>Tỷ lệ môn học đang mở</td>
                                    <td>{{ active_course_ratio|floatformat:2 }}%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Báo cáo đăng ký học -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Báo cáo đăng ký học</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Tổng số đăng ký</h5>
                                    <p class="display-4">{{ total_enrollments }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Đăng ký có điểm</h5>
                                    <p class="display-4">{{ graded_enrollments }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Điểm trung bình</h5>
                                    <p class="display-4">{{ avg_grade|floatformat:2 }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h5 class="mb-3">Phân bố điểm</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Điểm</th>
                                    <th>Số lượng</th>
                                    <th>Tỷ lệ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for grade, count in grade_distribution.items %}
                                <tr>
                                    <td>{{ grade }}</td>
                                    <td>{{ count }}</td>
                                    <td>{{ grade_percentages|get_item:grade|floatformat:2 }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
