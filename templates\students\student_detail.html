{% extends 'base.html' %}

{% block title %}{{ student.full_name }} - <PERSON><PERSON> thống Q<PERSON>ản lý Sin<PERSON> viên{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'student-list' %}">Sinh viên</a></li>
            <li class="breadcrumb-item active">{{ student.full_name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Student Info -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Thông tin sinh viên</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if student.photo %}
                        <img src="{{ student.photo.url }}" alt="{{ student.full_name }}" class="img-fluid rounded-circle mb-3" style="max-width: 150px; max-height: 150px;">
                        {% else %}
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                            <i class="fas fa-user-graduate fa-4x text-secondary"></i>
                        </div>
                        {% endif %}
                        <h4>{{ student.full_name }}</h4>
                        <p class="text-muted">Mã sinh viên: {{ student.student_id }}</p>
                        {% if student.is_active %}
                        <span class="badge bg-success">Đang học</span>
                        {% else %}
                        <span class="badge bg-secondary">Đã nghỉ</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Thông tin cá nhân</h6>
                        <hr>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Ngày sinh:</div>
                            <div class="col-7">{{ student.date_of_birth }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Giới tính:</div>
                            <div class="col-7">{{ student.get_gender_display }}</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Thông tin liên hệ</h6>
                        <hr>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Email:</div>
                            <div class="col-7">{{ student.email }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Điện thoại:</div>
                            <div class="col-7">{{ student.phone_number }}</div>
                        </div>
                        {% if student.address %}
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Địa chỉ:</div>
                            <div class="col-7">{{ student.address }}</div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <h6 class="text-primary">Thông tin học tập</h6>
                        <hr>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Ngày nhập học:</div>
                            <div class="col-7">{{ student.enrollment_date }}</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'student-list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại
                        </a>
                        {% if user.is_authenticated %}
                        <div>
                            <a href="{% url 'student-update' student.id %}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Chỉnh sửa
                            </a>
                            <a href="{% url 'student-delete' student.id %}" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Xóa
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Enrollments -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Đăng ký môn học</h5>
                </div>
                <div class="card-body">
                    {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Mã môn học</th>
                                    <th>Tên môn học</th>
                                    <th>Học kỳ</th>
                                    <th>Năm học</th>
                                    <th>Điểm</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.course.course_code }}</td>
                                    <td>
                                        <a href="{% url 'course-detail' enrollment.course.id %}">
                                            {{ enrollment.course.name }}
                                        </a>
                                    </td>
                                    <td>{{ enrollment.course.get_semester_display }}</td>
                                    <td>{{ enrollment.course.year }}</td>
                                    <td>
                                        {% if enrollment.grade %}
                                        <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% if enrollment.numeric_grade %}
                                        <small class="text-muted">({{ enrollment.numeric_grade }})</small>
                                        {% endif %}
                                        {% else %}
                                        <span class="badge bg-secondary">Chưa có điểm</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_authenticated %}
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'enrollment-update' enrollment.id %}" class="btn btn-warning" title="Cập nhật điểm">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'enrollment-delete' enrollment.id %}" class="btn btn-danger" title="Hủy đăng ký">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                        <p class="lead">Sinh viên chưa đăng ký môn học nào.</p>
                    </div>
                    {% endif %}
                </div>
                {% if user.is_authenticated %}
                <div class="card-footer text-end">
                    <a href="{% url 'enrollment-create' %}?student_id={{ student.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Đăng ký môn học
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
