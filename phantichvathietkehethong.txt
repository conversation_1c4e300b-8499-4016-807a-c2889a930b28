=====================================================
PHÂN TÍCH VÀ THIẾT KẾ HỆ THỐNG QUẢN LÝ SINH VIÊN
=====================================================

I. TỔNG QUAN HỆ THỐNG
---------------------

1. M<PERSON><PERSON> tiêu hệ thống
--------------------
Hệ thống Quản lý Sinh viên được phát triển nhằm mục đích:
- Quản lý thông tin sinh viên một cách hiệu quả
- Quản lý thông tin môn học và lịch học
- Quản lý đăng ký học và điểm số của sinh viên
- Cung cấp báo cáo thống kê về tình hình học tập
- Hỗ trợ quản trị viên trong việc ra quyết định

2. Phạm vi hệ thống
-------------------
Hệ thống bao gồm các chức năng:
- Quản lý thông tin sinh viên (thêm, sửa, xóa, xem)
- Quản lý thông tin môn học (thêm, sửa, xóa, xem)
- Quản lý đăng ký học (đăng ký, hủy đăng ký)
- Quản lý điểm số (nhập điểm, sửa điểm, xem điểm)
- Thống kê và báo cáo (sinh viên, môn học, điểm số)
- Quản lý người dùng và phân quyền

3. Đối tượng sử dụng
--------------------
- Quản trị viên: Có toàn quyền trên hệ thống
- Giáo viên: Quản lý môn học, nhập điểm
- Sinh viên: Xem thông tin cá nhân, đăng ký môn học, xem điểm
- Nhân viên quản lý: Quản lý thông tin sinh viên, xem báo cáo

II. YÊU CẦU HỆ THỐNG
--------------------

1. Yêu cầu chức năng
--------------------

1.1. Quản lý Sinh viên
- Thêm mới sinh viên với các thông tin: mã sinh viên, họ tên, ngày sinh, giới tính, email, số điện thoại, địa chỉ, ảnh đại diện
- Cập nhật thông tin sinh viên
- Xóa sinh viên khỏi hệ thống
- Tìm kiếm sinh viên theo nhiều tiêu chí (mã, tên, email)
- Xem danh sách sinh viên với phân trang
- Xem chi tiết thông tin sinh viên
- Xuất danh sách sinh viên ra file Excel/PDF

1.2. Quản lý Môn học
- Thêm mới môn học với các thông tin: mã môn học, tên môn học, mô tả, số tín chỉ, học kỳ, năm học
- Cập nhật thông tin môn học
- Xóa môn học khỏi hệ thống
- Tìm kiếm môn học theo nhiều tiêu chí
- Xem danh sách môn học với phân trang
- Xem chi tiết thông tin môn học
- Quản lý trạng thái môn học (đang mở/đã đóng)

1.3. Quản lý Đăng ký học
- Đăng ký môn học cho sinh viên
- Hủy đăng ký môn học
- Xem danh sách đăng ký theo sinh viên
- Xem danh sách đăng ký theo môn học
- Lọc danh sách đăng ký theo nhiều tiêu chí
- Kiểm tra điều kiện đăng ký (trùng lịch, đã đăng ký, etc.)

1.4. Quản lý Điểm số
- Nhập điểm cho sinh viên theo môn học
- Cập nhật điểm
- Xem bảng điểm của sinh viên
- Xem bảng điểm của môn học
- Tính điểm trung bình
- Phân loại kết quả học tập

1.5. Thống kê và Báo cáo
- Thống kê số lượng sinh viên (tổng số, đang học, đã nghỉ)
- Thống kê số lượng môn học (tổng số, đang mở, đã đóng)
- Thống kê đăng ký học (tổng số, theo môn học)
- Thống kê điểm số (điểm trung bình, phân bố điểm)
- Báo cáo tổng hợp theo học kỳ, năm học
- Biểu đồ trực quan hóa dữ liệu

1.6. Quản lý Người dùng
- Đăng nhập/Đăng xuất hệ thống
- Phân quyền người dùng
- Đổi mật khẩu
- Quản lý thông tin cá nhân

2. Yêu cầu phi chức năng
------------------------

2.1. Hiệu năng
- Thời gian phản hồi nhanh (< 2 giây)
- Hỗ trợ nhiều người dùng đồng thời
- Tối ưu hóa truy vấn cơ sở dữ liệu

2.2. Bảo mật
- Xác thực người dùng
- Phân quyền truy cập
- Mã hóa mật khẩu
- Bảo vệ dữ liệu cá nhân

2.3. Độ tin cậy
- Sao lưu dữ liệu định kỳ
- Khả năng phục hồi sau sự cố
- Xử lý lỗi và ngoại lệ

2.4. Khả năng sử dụng
- Giao diện thân thiện, dễ sử dụng
- Responsive design (hỗ trợ nhiều thiết bị)
- Hướng dẫn sử dụng rõ ràng
- Thông báo lỗi dễ hiểu

2.5. Khả năng mở rộng
- Kiến trúc module hóa
- Dễ dàng thêm tính năng mới
- Hỗ trợ tích hợp với các hệ thống khác

III. USE CASE DIAGRAM
---------------------

1. Actors (Tác nhân)
--------------------
- Administrator (Quản trị viên)
- Teacher (Giáo viên)
- Student (Sinh viên)
- Staff (Nhân viên quản lý)

2. Use Cases chính
-----------------

2.1. Use Cases cho Administrator
- Quản lý tài khoản người dùng
- Phân quyền người dùng
- Quản lý cấu hình hệ thống
- Xem tất cả báo cáo
- Sao lưu và phục hồi dữ liệu

2.2. Use Cases cho Teacher
- Đăng nhập/Đăng xuất
- Xem danh sách môn học
- Quản lý môn học
- Xem danh sách sinh viên đăng ký môn học
- Nhập và quản lý điểm số
- Xem báo cáo điểm số

2.3. Use Cases cho Student
- Đăng nhập/Đăng xuất
- Xem và cập nhật thông tin cá nhân
- Xem danh sách môn học
- Đăng ký môn học
- Hủy đăng ký môn học
- Xem điểm số cá nhân

2.4. Use Cases cho Staff
- Đăng nhập/Đăng xuất
- Quản lý thông tin sinh viên
- Quản lý thông tin môn học
- Quản lý đăng ký học
- Xem báo cáo và thống kê

3. Mô tả chi tiết Use Case
-------------------------

3.1. Use Case: Đăng nhập hệ thống
- Actor: Tất cả người dùng
- Mô tả: Người dùng đăng nhập vào hệ thống
- Tiền điều kiện: Người dùng có tài khoản hợp lệ
- Luồng chính:
  1. Người dùng truy cập trang đăng nhập
  2. Người dùng nhập tên đăng nhập và mật khẩu
  3. Hệ thống xác thực thông tin
  4. Hệ thống chuyển hướng đến trang chủ tương ứng với quyền hạn
- Luồng thay thế:
  * Nếu thông tin không hợp lệ, hiển thị thông báo lỗi
- Hậu điều kiện: Người dùng đăng nhập thành công

3.2. Use Case: Thêm mới sinh viên
- Actor: Administrator, Staff
- Mô tả: Thêm thông tin sinh viên mới vào hệ thống
- Tiền điều kiện: Người dùng đã đăng nhập và có quyền thêm sinh viên
- Luồng chính:
  1. Người dùng chọn chức năng "Thêm sinh viên"
  2. Người dùng nhập thông tin sinh viên
  3. Hệ thống kiểm tra tính hợp lệ của dữ liệu
  4. Hệ thống lưu thông tin sinh viên
  5. Hệ thống hiển thị thông báo thành công
- Luồng thay thế:
  * Nếu dữ liệu không hợp lệ, hiển thị thông báo lỗi
- Hậu điều kiện: Thông tin sinh viên được lưu vào hệ thống

3.3. Use Case: Đăng ký môn học
- Actor: Student, Staff
- Mô tả: Đăng ký môn học cho sinh viên
- Tiền điều kiện: Người dùng đã đăng nhập và có quyền đăng ký môn học
- Luồng chính:
  1. Người dùng chọn sinh viên (nếu là Staff)
  2. Người dùng chọn môn học cần đăng ký
  3. Hệ thống kiểm tra điều kiện đăng ký
  4. Hệ thống lưu thông tin đăng ký
  5. Hệ thống hiển thị thông báo thành công
- Luồng thay thế:
  * Nếu không đủ điều kiện đăng ký, hiển thị thông báo lỗi
- Hậu điều kiện: Sinh viên được đăng ký vào môn học

3.4. Use Case: Nhập điểm
- Actor: Teacher, Administrator
- Mô tả: Nhập điểm cho sinh viên theo môn học
- Tiền điều kiện: Người dùng đã đăng nhập và có quyền nhập điểm
- Luồng chính:
  1. Người dùng chọn môn học
  2. Hệ thống hiển thị danh sách sinh viên đăng ký môn học
  3. Người dùng nhập điểm cho từng sinh viên
  4. Hệ thống kiểm tra tính hợp lệ của điểm
  5. Hệ thống lưu thông tin điểm
  6. Hệ thống hiển thị thông báo thành công
- Luồng thay thế:
  * Nếu điểm không hợp lệ, hiển thị thông báo lỗi
- Hậu điều kiện: Điểm của sinh viên được lưu vào hệ thống

3.5. Use Case: Xem báo cáo thống kê
- Actor: Administrator, Staff, Teacher
- Mô tả: Xem các báo cáo thống kê về sinh viên, môn học và điểm số
- Tiền điều kiện: Người dùng đã đăng nhập và có quyền xem báo cáo
- Luồng chính:
  1. Người dùng chọn loại báo cáo cần xem
  2. Người dùng chọn các tham số lọc (nếu có)
  3. Hệ thống tạo báo cáo theo yêu cầu
  4. Hệ thống hiển thị báo cáo
- Luồng thay thế:
  * Nếu không có dữ liệu, hiển thị thông báo
- Hậu điều kiện: Báo cáo được hiển thị cho người dùng

IV. CLASS DIAGRAM
----------------

1. Các lớp chính trong hệ thống
------------------------------

1.1. Lớp User (Người dùng)
- Thuộc tính:
  * id: int
  * username: string
  * password: string
  * email: string
  * first_name: string
  * last_name: string
  * role: enum (ADMIN, TEACHER, STUDENT, STAFF)
  * is_active: boolean
  * last_login: datetime
- Phương thức:
  * authenticate(): boolean
  * changePassword(): boolean
  * getFullName(): string
  * getRoleDisplay(): string

1.2. Lớp Student (Sinh viên)
- Thuộc tính:
  * id: int
  * student_id: string
  * first_name: string
  * last_name: string
  * date_of_birth: date
  * gender: enum (MALE, FEMALE, OTHER)
  * email: string
  * phone_number: string
  * address: string
  * enrollment_date: date
  * photo: image
  * is_active: boolean
- Phương thức:
  * getFullName(): string
  * getAge(): int
  * getEnrollments(): List<Enrollment>
  * getGenderDisplay(): string

1.3. Lớp Course (Môn học)
- Thuộc tính:
  * id: int
  * course_code: string
  * name: string
  * description: string
  * credits: int
  * semester: enum (SEMESTER_1, SEMESTER_2, SUMMER)
  * year: int
  * start_date: date
  * end_date: date
  * is_active: boolean
- Phương thức:
  * getEnrollments(): List<Enrollment>
  * getStudentCount(): int
  * getSemesterDisplay(): string
  * getDuration(): int

1.4. Lớp Enrollment (Đăng ký học)
- Thuộc tính:
  * id: int
  * student: Student
  * course: Course
  * enrollment_date: date
  * grade: string
  * numeric_grade: float
- Phương thức:
  * isGraded(): boolean
  * getGradeStatus(): string
  * getGradeColor(): string

1.5. Lớp Report (Báo cáo)
- Thuộc tính:
  * id: int
  * title: string
  * description: string
  * created_date: datetime
  * report_type: enum (STUDENT, COURSE, GRADE, SUMMARY)
  * parameters: json
  * result: json
- Phương thức:
  * generateReport(): boolean
  * exportToPDF(): File
  * exportToExcel(): File

2. Mối quan hệ giữa các lớp
--------------------------

- User (1) -- (0..*) Student: Một User có thể liên kết với nhiều Student (trong trường hợp sinh viên có tài khoản)
- Student (1) -- (0..*) Enrollment: Một Student có thể có nhiều Enrollment
- Course (1) -- (0..*) Enrollment: Một Course có thể có nhiều Enrollment
- Enrollment (0..*) -- (1) Student: Nhiều Enrollment thuộc về một Student
- Enrollment (0..*) -- (1) Course: Nhiều Enrollment thuộc về một Course
- User (1) -- (0..*) Report: Một User có thể tạo nhiều Report

V. ACTIVITY DIAGRAM
------------------

1. Activity Diagram: Đăng nhập hệ thống
--------------------------------------
[Bắt đầu]
    |
    v
[Hiển thị form đăng nhập]
    |
    v
[Nhập username và password]
    |
    v
[Kiểm tra thông tin đăng nhập]
    |
    +---[Thông tin không hợp lệ]---> [Hiển thị thông báo lỗi] ---> [Hiển thị form đăng nhập]
    |
    +---[Thông tin hợp lệ]---> [Xác định quyền hạn người dùng]
                                    |
                                    v
                               [Chuyển hướng đến trang chủ tương ứng]
                                    |
                                    v
                                [Kết thúc]

2. Activity Diagram: Thêm mới sinh viên
--------------------------------------
[Bắt đầu]
    |
    v
[Kiểm tra quyền hạn]
    |
    +---[Không có quyền]---> [Hiển thị thông báo lỗi] ---> [Kết thúc]
    |
    +---[Có quyền]---> [Hiển thị form thêm sinh viên]
                            |
                            v
                       [Nhập thông tin sinh viên]
                            |
                            v
                       [Kiểm tra tính hợp lệ của dữ liệu]
                            |
                            +---[Dữ liệu không hợp lệ]---> [Hiển thị thông báo lỗi] ---> [Hiển thị form thêm sinh viên]
                            |
                            +---[Dữ liệu hợp lệ]---> [Lưu thông tin sinh viên vào CSDL]
                                                          |
                                                          v
                                                     [Hiển thị thông báo thành công]
                                                          |
                                                          v
                                                     [Chuyển đến trang danh sách sinh viên]
                                                          |
                                                          v
                                                      [Kết thúc]

3. Activity Diagram: Đăng ký môn học
-----------------------------------
[Bắt đầu]
    |
    v
[Kiểm tra quyền hạn]
    |
    +---[Không có quyền]---> [Hiển thị thông báo lỗi] ---> [Kết thúc]
    |
    +---[Có quyền]---> [Hiển thị danh sách môn học]
                            |
                            v
                       [Chọn môn học cần đăng ký]
                            |
                            v
                       [Chọn sinh viên (nếu là Staff)]
                            |
                            v
                       [Kiểm tra điều kiện đăng ký]
                            |
                            +---[Không đủ điều kiện]---> [Hiển thị thông báo lỗi] ---> [Hiển thị danh sách môn học]
                            |
                            +---[Đủ điều kiện]---> [Lưu thông tin đăng ký vào CSDL]
                                                        |
                                                        v
                                                   [Hiển thị thông báo thành công]
                                                        |
                                                        v
                                                   [Chuyển đến trang danh sách đăng ký]
                                                        |
                                                        v
                                                    [Kết thúc]

4. Activity Diagram: Nhập điểm
-----------------------------
[Bắt đầu]
    |
    v
[Kiểm tra quyền hạn]
    |
    +---[Không có quyền]---> [Hiển thị thông báo lỗi] ---> [Kết thúc]
    |
    +---[Có quyền]---> [Hiển thị danh sách môn học]
                            |
                            v
                       [Chọn môn học]
                            |
                            v
                       [Hiển thị danh sách sinh viên đăng ký môn học]
                            |
                            v
                       [Nhập điểm cho từng sinh viên]
                            |
                            v
                       [Kiểm tra tính hợp lệ của điểm]
                            |
                            +---[Điểm không hợp lệ]---> [Hiển thị thông báo lỗi] ---> [Nhập lại điểm]
                            |
                            +---[Điểm hợp lệ]---> [Lưu thông tin điểm vào CSDL]
                                                       |
                                                       v
                                                  [Hiển thị thông báo thành công]
                                                       |
                                                       v
                                                  [Kết thúc]

5. Activity Diagram: Xem báo cáo thống kê
----------------------------------------
[Bắt đầu]
    |
    v
[Kiểm tra quyền hạn]
    |
    +---[Không có quyền]---> [Hiển thị thông báo lỗi] ---> [Kết thúc]
    |
    +---[Có quyền]---> [Hiển thị danh sách loại báo cáo]
                            |
                            v
                       [Chọn loại báo cáo]
                            |
                            v
                       [Nhập tham số lọc (nếu cần)]
                            |
                            v
                       [Tạo báo cáo]
                            |
                            +---[Không có dữ liệu]---> [Hiển thị thông báo] ---> [Hiển thị danh sách loại báo cáo]
                            |
                            +---[Có dữ liệu]---> [Hiển thị báo cáo]
                                                      |
                                                      v
                                                 [Xuất báo cáo (nếu cần)]
                                                      |
                                                      v
                                                  [Kết thúc]

VI. SEQUENCE DIAGRAM
-------------------

1. Sequence Diagram: Đăng nhập hệ thống
-------------------------------------
[User] --> [LoginView]: Truy cập trang đăng nhập
[LoginView] --> [User]: Hiển thị form đăng nhập
[User] --> [LoginView]: Nhập username và password
[LoginView] --> [AuthenticationService]: authenticate(username, password)
[AuthenticationService] --> [UserRepository]: findByUsername(username)
[UserRepository] --> [AuthenticationService]: return user
[AuthenticationService] --> [PasswordEncoder]: matches(password, encodedPassword)
[PasswordEncoder] --> [AuthenticationService]: return result
[AuthenticationService] --> [LoginView]: return authentication result
[LoginView] --> [User]: Hiển thị thông báo lỗi (nếu có)
[LoginView] --> [HomeController]: redirect (nếu thành công)
[HomeController] --> [User]: Hiển thị trang chủ

2. Sequence Diagram: Thêm mới sinh viên
-------------------------------------
[User] --> [StudentController]: Truy cập trang thêm sinh viên
[StudentController] --> [AuthorizationService]: checkPermission(user, "ADD_STUDENT")
[AuthorizationService] --> [StudentController]: return result
[StudentController] --> [User]: Hiển thị form thêm sinh viên
[User] --> [StudentController]: submitForm(studentData)
[StudentController] --> [StudentValidator]: validate(studentData)
[StudentValidator] --> [StudentController]: return validation result
[StudentController] --> [StudentService]: createStudent(studentData)
[StudentService] --> [StudentRepository]: save(student)
[StudentRepository] --> [StudentService]: return saved student
[StudentService] --> [StudentController]: return result
[StudentController] --> [User]: Hiển thị thông báo thành công
[StudentController] --> [User]: Chuyển hướng đến trang danh sách sinh viên

VII. KIẾN TRÚC HỆ THỐNG
----------------------

1. Kiến trúc tổng thể
--------------------
Hệ thống được xây dựng theo mô hình MVC (Model-View-Controller) với các thành phần chính:

- Presentation Layer (View): Giao diện người dùng, hiển thị dữ liệu và tương tác với người dùng
- Business Logic Layer (Controller): Xử lý logic nghiệp vụ, điều hướng luồng dữ liệu
- Data Access Layer (Model): Quản lý dữ liệu, tương tác với cơ sở dữ liệu

2. Công nghệ sử dụng
-------------------
- Ngôn ngữ lập trình: Python
- Framework: Django
- Cơ sở dữ liệu: SQLite
- Frontend: HTML, CSS, JavaScript, Bootstrap
- Công cụ phát triển: Visual Studio Code, Git

3. Cấu trúc thư mục
------------------
```
quanlysinhvien/
├── quanlysinhvien/          # Thư mục cấu hình chính của dự án
│   ├── __init__.py
│   ├── settings.py          # Cấu hình dự án
│   ├── urls.py              # Định tuyến URL chính
│   ├── views.py             # Views chung của dự án
│   ├── wsgi.py              # Cấu hình WSGI
│   └── asgi.py              # Cấu hình ASGI
├── students/                # Ứng dụng quản lý sinh viên
│   ├── __init__.py
│   ├── admin.py             # Cấu hình admin
│   ├── apps.py              # Cấu hình ứng dụng
│   ├── models.py            # Mô hình dữ liệu
│   ├── views.py             # Xử lý logic và hiển thị
│   ├── urls.py              # Định tuyến URL
│   ├── forms.py             # Định nghĩa form
│   └── tests.py             # Kiểm thử
├── courses/                 # Ứng dụng quản lý môn học
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── forms.py
│   └── tests.py
├── templates/               # Thư mục chứa templates HTML
│   ├── base.html            # Template cơ sở
│   ├── home.html            # Trang chủ
│   ├── students/            # Templates cho ứng dụng students
│   └── courses/             # Templates cho ứng dụng courses
├── static/                  # Thư mục chứa tài nguyên tĩnh
│   ├── css/                 # Stylesheet
│   ├── js/                  # JavaScript
│   └── img/                 # Hình ảnh
├── media/                   # Thư mục chứa file người dùng tải lên
├── manage.py                # Script quản lý Django
└── requirements.txt         # Danh sách các gói phụ thuộc
```

VIII. KẾ HOẠCH TRIỂN KHAI
------------------------

1. Giai đoạn phát triển
----------------------
- Phân tích yêu cầu: 1 tuần
- Thiết kế hệ thống: 1 tuần
- Phát triển backend: 2 tuần
- Phát triển frontend: 2 tuần
- Kiểm thử: 1 tuần
- Sửa lỗi và hoàn thiện: 1 tuần

2. Kế hoạch kiểm thử
-------------------
- Unit Testing: Kiểm thử từng thành phần riêng lẻ
- Integration Testing: Kiểm thử tích hợp giữa các thành phần
- System Testing: Kiểm thử toàn bộ hệ thống
- User Acceptance Testing: Kiểm thử chấp nhận người dùng

3. Kế hoạch triển khai
---------------------
- Chuẩn bị môi trường: Cài đặt và cấu hình máy chủ
- Triển khai ứng dụng: Cài đặt ứng dụng lên máy chủ
- Cấu hình cơ sở dữ liệu: Tạo và cấu hình cơ sở dữ liệu
- Kiểm tra triển khai: Kiểm tra hoạt động của hệ thống
- Đào tạo người dùng: Hướng dẫn sử dụng hệ thống
- Bàn giao và hỗ trợ: Bàn giao hệ thống và cung cấp hỗ trợ kỹ thuật

IX. KẾT LUẬN
-----------
Hệ thống Quản lý Sinh viên được thiết kế để đáp ứng nhu cầu quản lý thông tin sinh viên, môn học và đăng ký học một cách hiệu quả. Hệ thống cung cấp giao diện thân thiện, dễ sử dụng và các chức năng đầy đủ để hỗ trợ công tác quản lý sinh viên.

Với kiến trúc module hóa, hệ thống dễ dàng mở rộng và bảo trì. Các công nghệ hiện đại được sử dụng giúp hệ thống hoạt động ổn định, bảo mật và hiệu quả.

Hệ thống này sẽ giúp tối ưu hóa quy trình quản lý sinh viên, giảm thiểu công việc thủ công và nâng cao hiệu quả làm việc của đội ngũ quản lý.
