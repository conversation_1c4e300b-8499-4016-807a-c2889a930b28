#!/usr/bin/env python
"""
Final test để xác minh hệ thống hoạt động
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from students.models import Student

def test_system():
    """Test toàn bộ hệ thống"""
    print("🔍 KIỂM TRA CUỐI CÙNG HỆ THỐNG")
    print("=" * 50)
    
    # Test 1: Kiểm tra tài khoản student_test
    print("1️⃣ Test tài khoản student_test...")
    client = Client()
    login_success = client.login(username='student_test', password='student123')
    print(f"   Login: {'✅ Thành công' if login_success else '❌ Thất bại'}")
    
    if login_success:
        # Test student-profile
        response = client.get('/student-profile/')
        print(f"   /student-profile/: {'✅ OK' if response.status_code == 200 else f'❌ {response.status_code}'}")
        
        # Test my-courses
        response = client.get('/my-courses/')
        print(f"   /my-courses/: {'✅ OK' if response.status_code == 200 else f'❌ {response.status_code}'}")
        
        # Test admin pages (should be blocked)
        response = client.get('/dashboard/')
        print(f"   /dashboard/: {'✅ Blocked' if response.status_code == 302 else f'❌ Not blocked ({response.status_code})'}")
        
        response = client.get('/students/create/')
        print(f"   /students/create/: {'✅ Blocked' if response.status_code == 302 else f'❌ Not blocked ({response.status_code})'}")
    
    client.logout()
    
    # Test 2: Kiểm tra tài khoản admin
    print("\n2️⃣ Test tài khoản admin...")
    login_success = client.login(username='admin_test', password='admin123')
    print(f"   Login: {'✅ Thành công' if login_success else '❌ Thất bại'}")
    
    if login_success:
        # Test admin pages
        response = client.get('/dashboard/')
        print(f"   /dashboard/: {'✅ OK' if response.status_code == 200 else f'❌ {response.status_code}'}")
        
        response = client.get('/students/')
        print(f"   /students/: {'✅ OK' if response.status_code == 200 else f'❌ {response.status_code}'}")
    
    # Test 3: Kiểm tra dữ liệu
    print("\n3️⃣ Kiểm tra dữ liệu...")
    total_users = User.objects.count()
    total_students = Student.objects.count()
    print(f"   Tổng users: {total_users}")
    print(f"   Tổng students: {total_students}")
    
    # Test 4: Kiểm tra một số student accounts mới
    print("\n4️⃣ Test student accounts mới...")
    sample_students = Student.objects.all()[:3]
    for student in sample_students:
        username = student.student_id.lower()
        try:
            user = User.objects.get(username=username)
            client_test = Client()
            login_success = client_test.login(username=username, password=student.student_id)
            print(f"   {username}: {'✅ Login OK' if login_success else '❌ Login failed'}")
            
            if login_success:
                response = client_test.get('/student-profile/')
                print(f"      Profile: {'✅ OK' if response.status_code == 200 else f'❌ {response.status_code}'}")
        except User.DoesNotExist:
            print(f"   {username}: ⏳ User chưa được tạo")
    
    print("\n" + "=" * 50)
    print("✅ KIỂM TRA HOÀN TẤT!")

if __name__ == '__main__':
    test_system()
