#!/usr/bin/env python
"""
Simple test để kiểm tra phân quyền
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student

def main():
    print("🔒 KIỂM TRA NHANH HỆ THỐNG PHÂN QUYỀN")
    print("=" * 50)
    
    # Kiểm tra user đã tạo
    try:
        admin_user = User.objects.get(username='admin_test')
        print(f"✓ Admin user: {admin_user.username} (is_staff: {admin_user.is_staff})")
    except User.DoesNotExist:
        print("✗ Admin user không tồn tại")
        return
    
    try:
        student_user = User.objects.get(username='student_test')
        print(f"✓ Student user: {student_user.username} (is_staff: {student_user.is_staff})")
    except User.DoesNotExist:
        print("✗ Student user không tồn tại")
        return
    
    try:
        student_record = Student.objects.get(email='<EMAIL>')
        print(f"✓ Student record: {student_record.student_id}")
    except Student.DoesNotExist:
        print("✗ Student record không tồn tại")
        return
    
    print("\n=== KIỂM TRA PHÂN QUYỀN ===")
    print(f"Admin user is_staff: {admin_user.is_staff}")
    print(f"Admin user is_superuser: {admin_user.is_superuser}")
    print(f"Student user is_staff: {student_user.is_staff}")
    print(f"Student user is_superuser: {student_user.is_superuser}")
    
    if admin_user.is_staff and not student_user.is_staff:
        print("✅ PHÂN QUYỀN ĐÚNG: Admin có quyền staff, Student không có")
    else:
        print("❌ PHÂN QUYỀN SAI!")
    
    print("\n=== HƯỚNG DẪN TEST THỦ CÔNG ===")
    print("1. Mở browser và truy cập: http://127.0.0.1:8000/login/")
    print("2. Đăng nhập với tài khoản sinh viên:")
    print("   - Username: student_test")
    print("   - Password: student123")
    print("3. Sau khi đăng nhập, kiểm tra:")
    print("   - Navigation menu chỉ hiển thị: 'Thông tin cá nhân', 'Môn học của tôi'")
    print("   - Không hiển thị: 'Sinh viên', 'Môn học', 'Đăng ký học', 'Thống kê', 'Báo cáo'")
    print("4. Thử truy cập trực tiếp các URL admin:")
    print("   - http://127.0.0.1:8000/dashboard/ -> Phải bị từ chối")
    print("   - http://127.0.0.1:8000/students/ -> Phải bị từ chối")
    print("   - http://127.0.0.1:8000/students/create/ -> Phải bị từ chối")
    print("   - http://127.0.0.1:8000/courses/create/ -> Phải bị từ chối")
    print("5. Thử truy cập URL sinh viên:")
    print("   - http://127.0.0.1:8000/student-profile/ -> Phải được phép")
    print("   - http://127.0.0.1:8000/my-courses/ -> Phải được phép")
    
    print("\n6. Đăng xuất và đăng nhập với admin:")
    print("   - Username: admin_test")
    print("   - Password: admin123")
    print("7. Kiểm tra admin có thể truy cập tất cả chức năng")

if __name__ == '__main__':
    main()
